import json
from plugins_func.register import register_function,ToolType, ActionResponse, Action
from config.logger import setup_logging
import requests

TAG = __name__
logger = setup_logging()

change_scene_function_desc = {
                "type": "function",
                "function": {
                    "name": "change_scene",
                    "description": "当用户明确表示要切换、设定新的情景时，请调用此函数",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "description": {
                                "type": "string",
                                "description": "用户设定情景的原始“描述和信息”，不需加工"
                            }
                        },
                        "required": ["description"]
                    }
                }
            }

def generate_topic(description: str):
    url = "http://dify.lezhilong.cn/v1/chat-messages"
    headers = {
        "Authorization": "Bearer app-sD6CBYg6IcLgF9NNOai9Z3Wj",
        "Content-Type": "application/json"
    }
    payload = {
        "inputs": {
            "nickname": "乐乐"
        },
        "query": description,
        "response_mode": "blocking",
        "user": "test_user_001"
    }
    response = requests.post(url, json=payload, headers=headers)
    response.raise_for_status()
    return response.json() 
@register_function('change_scene', change_scene_function_desc, ToolType.WAIT)
def change_scene(description: str):
    """切换情景"""
    
    logger.bind(tag=TAG).info(f"准备情景:{description}")
    result = generate_topic(description)
    logger.bind(tag=TAG).info(result)
    answer = json.loads(result.get("answer"))
    pilot = answer.get("pilot")
    logger.bind(tag=TAG).info(f"生成新的主动话题:{pilot}")
    return ActionResponse(action=Action.RESPONSE, result="切换情景已处理", response=pilot)
