diff --git a/core/connection.py b/core/connection.py
index 65bc54a..5e31430 100644
--- a/core/connection.py
+++ b/core/connection.py
@@ -11,6 +11,7 @@ import traceback
 import subprocess
 import websockets
 from core.handle.mcpHandle import call_mcp_tool
+from core.handle.sendAudioHandle import send_stt_message
 from core.utils.audio import mp3_to_opus_stream
 from core.utils.util import (
     extract_json_from_string,
@@ -997,7 +998,7 @@ class ConnectionHandler:
         except Exception as e:
             self.logger.bind(tag=TAG).error(f"超时检查任务出错: {e}")
 
-    def send_assistant_text(self, text):
+    async def send_assistant_text(self, text):
         """
         发送固定 assistant 类型的文本内容到客户端，包括对话历史和 TTS 队列
         """
@@ -1008,13 +1009,18 @@ class ConnectionHandler:
         sentence_id = str(uuid.uuid4()).replace("-", "")
         # 存储到对话历史
         self.dialogue.put(Message(role="assistant", content=text))
+
+        # 设置llm任务完成
+        self.llm_finish_task = True
+        #  发送STT消息
+        await send_stt_message(self, text)
+
         # 推送到TTS队列
         self.tts.tts_text_queue.put(
             TTSMessageDTO(
                 sentence_id=sentence_id,
                 sentence_type=SentenceType.FIRST,
                 content_type=ContentType.ACTION,
-                content_detail=text,
             )
         )
 
diff --git a/core/handle/textHandle.py b/core/handle/textHandle.py
index 672810a..8001f69 100644
--- a/core/handle/textHandle.py
+++ b/core/handle/textHandle.py
@@ -166,7 +166,7 @@ async def handleTextMessage(conn, message):
                 # await send_tts_message(conn, "sentence_start", text)  # 播报语音
                 # await send_tts_message(conn, "sentence_stop", text)  # 播报语音
 
-                conn.send_assistant_text(text)
+                await conn.send_assistant_text(text)
         else:
             conn.logger.bind(tag=TAG).error(f"收到未知类型消息：{message}")
     except json.JSONDecodeError:
