#!/usr/bin/env python3
"""
测试 WAV 到 OPUS 转换功能
"""

import os
import sys
import numpy as np
import wave

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_test_wav_file(filename, duration_ms=1000, sample_rate=16000, frequency=440):
    """创建测试用的 WAV 文件
    
    Args:
        filename: 输出文件名
        duration_ms: 音频时长（毫秒）
        sample_rate: 采样率
        frequency: 音频频率（Hz）
    """
    num_samples = int(sample_rate * duration_ms / 1000)
    t = np.linspace(0, duration_ms / 1000, num_samples, False)
    
    # 生成正弦波
    wave_data = np.sin(2 * np.pi * frequency * t)
    
    # 转换为 16 位整数
    wave_data = (wave_data * 32767).astype(np.int16)
    
    with wave.open(filename, 'wb') as wf:
        wf.setnchannels(1)  # 单声道
        wf.setsampwidth(2)  # 16位
        wf.setframerate(sample_rate)
        wf.writeframes(wave_data.tobytes())

def test_wav_conversion():
    """测试 WAV 转换功能"""
    print("测试 WAV 到 OPUS 转换功能...")
    
    try:
        # 导入转换函数
        from core.handle.receiveAudioHandle import detect_audio_format, wav_to_opus
        
        # 创建测试 WAV 文件
        test_wav_file = "test_conversion.wav"
        create_test_wav_file(test_wav_file, 1000, 16000, 440)  # 1秒音频
        
        # 读取 WAV 文件
        with open(test_wav_file, 'rb') as f:
            wav_data = f.read()
        
        print(f"原始 WAV 文件大小: {len(wav_data)} 字节")
        
        # 测试格式检测
        format_result = detect_audio_format(wav_data)
        print(f"格式检测结果: {format_result}")
        assert format_result == 'wav', f"期望 'wav'，得到 '{format_result}'"
        
        # 测试转换
        opus_packets = wav_to_opus(wav_data)
        print(f"转换结果: {len(opus_packets)} 个 OPUS 数据包")
        
        if opus_packets:
            total_opus_size = sum(len(packet) for packet in opus_packets)
            print(f"总 OPUS 数据大小: {total_opus_size} 字节")
            print(f"每个数据包大小: {[len(packet) for packet in opus_packets[:5]]}...")  # 显示前5个
            
            # 验证数据包不为空
            for i, packet in enumerate(opus_packets):
                assert len(packet) > 0, f"数据包 {i} 为空"
            
            print("✓ WAV 到 OPUS 转换测试通过")
        else:
            print("✗ 转换失败，没有生成 OPUS 数据包")
            return False
        
        # 清理测试文件
        if os.path.exists(test_wav_file):
            os.remove(test_wav_file)
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        # 清理测试文件
        if os.path.exists("test_conversion.wav"):
            os.remove("test_conversion.wav")
        return False

def test_pcm_conversion():
    """测试纯 PCM 数据转换"""
    print("测试纯 PCM 数据转换...")
    
    try:
        from core.handle.receiveAudioHandle import detect_audio_format, wav_to_opus
        
        # 创建 PCM 数据（1920 字节 = 960 samples * 2 bytes）
        num_samples = 960
        t = np.linspace(0, 0.06, num_samples, False)  # 60ms
        wave_data = np.sin(2 * np.pi * 440 * t)
        pcm_data = (wave_data * 32767).astype(np.int16).tobytes()
        
        print(f"PCM 数据大小: {len(pcm_data)} 字节")
        
        # 测试格式检测
        format_result = detect_audio_format(pcm_data)
        print(f"格式检测结果: {format_result}")
        assert format_result == 'wav', f"期望 'wav'，得到 '{format_result}'"
        
        # 测试转换
        opus_packets = wav_to_opus(pcm_data)
        print(f"转换结果: {len(opus_packets)} 个 OPUS 数据包")
        
        if opus_packets:
            assert len(opus_packets) == 1, f"期望 1 个数据包，得到 {len(opus_packets)} 个"
            assert len(opus_packets[0]) > 0, "OPUS 数据包为空"
            print(f"OPUS 数据包大小: {len(opus_packets[0])} 字节")
            print("✓ PCM 数据转换测试通过")
        else:
            print("✗ 转换失败，没有生成 OPUS 数据包")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试 WAV 到 OPUS 转换功能...")
    print("=" * 50)
    
    tests = [
        test_wav_conversion,
        test_pcm_conversion
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
            print()
        except Exception as e:
            print(f"✗ 测试 {test_func.__name__} 出现异常: {e}")
            print()
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！WAV 到 OPUS 转换功能正常")
        return True
    else:
        print("❌ 部分测试失败，请检查实现")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
