from pydub import AudioSegment
import opuslib_next
import numpy as np
import io

def mp3_to_opus_stream(mp3_chunk, sample_rate=16000, bitrate=64000) -> bytes:
    """
    流式将 MP3 分块转换为 Opus Bytes
    
    Args:
        mp3_stream: 可迭代的 MP3 数据块（bytes）
        sample_rate: 目标采样率（默认 48000）
        bitrate: 目标比特率（默认 64kbps）
    
    Returns:
        Opus 编码后的完整字节数据（bytes）
    """
    # 初始化 Opus 编码器
    encoder = opuslib_next.Encoder(sample_rate, channels=1, application="audio")
    encoder.bitrate = bitrate
    
    # 用 BytesIO 动态收集 Opus 数据
    opus_buffer = io.BytesIO()
    
    # 1. 解码 MP3 分块 → PCM
    audio = AudioSegment.from_mp3(io.BytesIO(mp3_chunk))
    audio = audio.set_channels(1).set_frame_rate(sample_rate)
    samples = np.array(audio.get_array_of_samples(), dtype=np.int16)
    
    # 2. 分块编码 PCM → Opus
    frame_size = 960  # 20ms 帧（48kHz 时）
    for i in range(0, len(samples), frame_size):
        pcm_chunk = samples[i:i+frame_size].tobytes()
        opus_chunk = encoder.encode(pcm_chunk, frame_size)
        opus_buffer.write(opus_chunk)
    
    return opus_buffer.getvalue()  # 返回所有 Opus Bytes